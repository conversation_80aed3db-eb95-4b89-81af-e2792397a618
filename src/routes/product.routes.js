import express from "express";
import {
  aggregateProducts,
  createProduct,
  deleteProduct,
  getAllProducts,
  getProductById,
  getProductBySlug,
  getProductsByType,
  getProductsCursor,
  getProductsPagination,
  updateProduct,
} from "../controllers/product.controller.js";
import { requireAuth } from "@clerk/express";
import { authorize } from "../middlewares/auth.middleware.js";
import { validateProduct } from "../validators/product.validator.js";

const router = express.Router();

// Public routes
router.get("/", getAllProducts); // Fetch all products
router.get("/type/:type", getProductsByType); // Fetch products by category
router.get("/slug/:slug", getProductBySlug); // Fetch a single product by slug
router.get("/cursor", getProductsCursor); // Fetch products using cursor-based pagination
router.get("/aggregation", aggregateProducts); // Fetch products using aggregation pipeline

// Admin-only routes
router.use(requireAuth());
router.use(authorize(["admin", "super_admin"]));

router.post("/", validateProduct, createProduct); // Create a new product
router.get("/admin/pagination", getProductsPagination); // Fetch products with admin pagination
router.get("/:id", getProductById); // Fetch a single product by ID
router.put("/:id", updateProduct); // Update an existing product by ID
router.delete("/:id", deleteProduct); // Delete a product by ID

export default router;
