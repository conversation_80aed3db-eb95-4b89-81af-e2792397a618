import express from "express";
import { requireAuth } from "@clerk/express";
import { authorize } from "../middlewares/auth.middleware.js";
import {
  syncSubscriptionStatus,
  checkProductAccess,
  checkDownloadLimits,
} from "../middlewares/subscription.middleware.js";
import {
  checkProductAccess as checkAccess,
  generateDownloadLink,
  secureDownload,
  getDownloadHistory,
  getUserAccessPermissions,
  grantProductAccess,
  revokeProductAccess,
} from "../controllers/access.controller.js";

const router = express.Router();

// Public route for secure downloads (token-based authentication)
router.get("/secure-download/:token", secureDownload);

// Protected routes requiring authentication
router.use(requireAuth());
router.use(syncSubscriptionStatus);

// User access routes
router.get("/check/:productId", checkAccess);
router.get("/permissions", getUserAccessPermissions);
router.get("/download-history", getDownloadHistory);

// Download routes with access control
router.post(
  "/download/:productId",
  checkProductAccess,
  checkDownloadLimits,
  generateDownloadLink
);

// Admin routes for managing access
router.use(authorize(["admin", "super_admin"]));
router.post("/grant", grantProductAccess);
router.post("/revoke", revokeProductAccess);

export default router;
