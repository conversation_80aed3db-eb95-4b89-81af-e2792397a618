import express from "express";
import {
  createFolder,
  createFile,
  getFolderContents,
  searchFileSystem,
  getItemById,
  updateItem,
  deleteItems,
  permanentDeleteItems,
  restoreItems,
  getFilesByType,
} from "../controllers/fileSystem.controller.js";
import { authorize } from "../middlewares/auth.middleware.js";
import { requireAuth } from "@clerk/express";

const router = express.Router();

// Apply authentication to all routes
router.use(requireAuth());

// Public routes (authenticated users)
router.get("/folder/:folderId/contents", getFolderContents); // Get folder contents
router.get("/search", searchFileSystem); // Search files and folders
router.get("/files/by-type", getFilesByType); // Get files by MIME type
router.get("/:id", getItemById); // Get item by ID

// Admin/Super Admin routes
router.use(authorize(["admin", "super_admin"]));

// Folder management
router.post("/folder", createFolder); // Create folder

// File management
router.post("/file", createFile); // Create/upload file

// Item management
router.put("/:id", updateItem); // Update item
router.delete("/soft-delete", deleteItems); // Soft delete items
router.delete("/permanent-delete", permanentDeleteItems); // Permanently delete items
router.post("/restore", restoreItems); // Restore deleted items

export default router;
