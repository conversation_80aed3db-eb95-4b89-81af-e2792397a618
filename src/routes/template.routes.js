import express from "express";
import {
  createTemplate,
  getTemplates,
  getTemplateById,
  updateTemplate,
  deleteTemplate,
} from "../controllers/template.controller.js";
import { authorize } from "../middlewares/auth.middleware.js";
import { requireAuth } from "@clerk/express";

const router = express.Router();
router.use(requireAuth());
router.use(authorize(['admin',"super_admin"]));

// Create a new template
router.post("/", createTemplate);

// Get all templates
router.get("/", getTemplates);

// Get a single template by ID
router.get("/:id", getTemplateById);

// Update a template
router.put("/:id", updateTemplate);

// Delete a template
router.delete("/:id", deleteTemplate);

export default router;
