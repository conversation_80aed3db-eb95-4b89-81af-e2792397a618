import express from "express";
import {
  createCoupon,
  getCoupons,
  getCouponById,
  updateCoupon,
  deleteCoupon,
  getCouponsWithPagination,
} from "../controllers/coupon.controller.js";

import { validateCoupon } from "../validators/coupon.validator.js";
import { requireAuth } from "@clerk/express";
import { authorize } from "../middlewares/auth.middleware.js";

const router = express.Router();


router.use(requireAuth());
router.use(authorize(["admin", "super_admin"]));
// Create a new coupon
router.post("/", validateCoupon, createCoupon);

// Get all coupons
router.get("/", getCoupons);

/**
 * @route GET /coupons (pagination)
 * @desc Get all coupons with pagination
 */

router.get("/pagination", getCouponsWithPagination);

// Get a coupon by ID
router.get("/:id", getCouponById);

// Update a coupon
router.put("/:id", validateCoupon, updateCoupon);

// Delete a coupon
router.delete("/:id", deleteCoupon);

export default router;
