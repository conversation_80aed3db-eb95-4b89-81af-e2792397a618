# DesignByte Authentication Migration Guide

This guide will help you migrate from Clerk authentication to our custom authentication system with email/password and Google OAuth.

## 🚀 What's Changed

### Removed
- ❌ Clerk authentication system
- ❌ Complex subscription model with multiple nested objects
- ❌ Redundant user fields (accessLevel, subscriptionMetadata, etc.)
- ❌ Complex transaction model with nested billing/plan details

### Added
- ✅ Custom JWT-based authentication
- ✅ Email/password authentication with verification
- ✅ Google OAuth 2.0 integration
- ✅ Redis session management with Upstash
- ✅ Simplified user and transaction models
- ✅ Password reset functionality
- ✅ Email verification system

## 📋 Prerequisites

Before starting the migration, ensure you have:

1. **Database Backup**: Create a backup of your MongoDB database
2. **Environment Variables**: Prepare the new environment variables
3. **Email Service**: Set up SMTP for email sending
4. **Google OAuth**: Create Google OAuth credentials
5. **Redis**: Set up Upstash Redis account

## 🔧 Setup Instructions

### 1. Environment Configuration

Copy `.env.example` to `.env` and configure the following:

```bash
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=30d

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_NAME=DesignByte
FROM_EMAIL=<EMAIL>

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token
```

### 2. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:5000/api/auth/google/callback` (development)
   - `https://yourdomain.com/api/auth/google/callback` (production)

### 3. Upstash Redis Setup

1. Go to [Upstash Console](https://console.upstash.com/)
2. Create a new Redis database
3. Copy the REST URL and token to your `.env` file

### 4. Email Service Setup

For Gmail SMTP:
1. Enable 2-factor authentication on your Google account
2. Generate an App Password
3. Use the App Password in `SMTP_PASS`

### 5. Run Migration

```bash
# Install dependencies
npm install

# Run the migration script
npm run migrate
```

## 🔄 Migration Process

The migration script will:

1. **Remove Clerk fields** from user documents
2. **Simplify subscription fields** in user model
3. **Consolidate transaction data** into metadata field
4. **Generate usernames** for existing users
5. **Set default passwords** for email users (they'll need to reset)
6. **Update indexes** for new fields

## 📚 API Changes

### Authentication Endpoints

| Old Endpoint | New Endpoint | Method | Description |
|-------------|-------------|---------|-------------|
| N/A (Clerk) | `/api/auth/register` | POST | Register with email/password |
| N/A (Clerk) | `/api/auth/login` | POST | Login with email/password |
| N/A (Clerk) | `/api/auth/google` | GET | Google OAuth login |
| N/A (Clerk) | `/api/auth/forgot-password` | POST | Request password reset |
| N/A (Clerk) | `/api/auth/reset-password` | POST | Reset password |
| N/A (Clerk) | `/api/auth/verify-email` | POST | Verify email address |
| N/A (Clerk) | `/api/auth/me` | GET | Get current user |
| N/A (Clerk) | `/api/auth/logout` | POST | Logout user |

### User Model Changes

```javascript
// Old fields (removed)
clerkId, subscriptionTier, subscriptionStatus, subscriptionExpiry,
currentPlanDetails, autoRenew, nextRenewalDate, cancellation,
trial, accessLevel, lastTransaction, subscriptionMetadata

// New fields (added)
username, provider, isEmailVerified, emailVerificationToken,
passwordResetToken, passwordResetExpires

// Simplified subscription fields
isPro, isLifetimePro, subscriptionStartDate, subscriptionEndDate, currentPlan
```

### Transaction Model Changes

```javascript
// Old fields (removed)
userSubscriptionData, planDetails, couponDetails, billingDetails

// Simplified
metadata: { type: Mixed } // All additional data goes here
```

## 🧪 Testing

### Manual Testing

1. **Registration**: Test email registration with verification
2. **Login**: Test email/password login
3. **Google OAuth**: Test Google login flow
4. **Password Reset**: Test forgot/reset password flow
5. **Protected Routes**: Test JWT authentication
6. **Subscription**: Test subscription activation/management

### API Testing with curl

```bash
# Register
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123","firstName":"Test","lastName":"User","username":"testuser"}'

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123"}'

# Get current user (replace TOKEN with actual JWT)
curl -X GET http://localhost:5000/api/auth/me \
  -H "Authorization: Bearer TOKEN"
```

## 🚨 Important Notes

1. **Existing Users**: Users migrated from Clerk will need to reset their passwords
2. **Sessions**: All existing sessions will be invalidated
3. **Frontend**: Update your frontend to use the new authentication endpoints
4. **Email Verification**: New users must verify their email addresses
5. **Google Users**: Existing Google users will be automatically linked

## 🔒 Security Features

- JWT tokens with configurable expiration
- Password hashing with bcrypt
- Email verification for new accounts
- Password reset with secure tokens
- Rate limiting with Redis
- Session management with Redis
- CORS protection
- Input validation and sanitization

## 📞 Support

If you encounter any issues during migration:

1. Check the migration logs for errors
2. Verify all environment variables are set correctly
3. Ensure database connectivity
4. Test email service configuration
5. Verify Google OAuth setup

## 🔄 Rollback

If you need to rollback (not recommended):

1. Restore your database backup
2. Revert to the previous codebase
3. Restore Clerk configuration

**Note**: The migration script includes basic rollback logging, but manual intervention may be required for complete rollback.
